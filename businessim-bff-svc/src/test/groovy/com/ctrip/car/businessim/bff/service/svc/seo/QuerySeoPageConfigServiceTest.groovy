package com.ctrip.car.businessim.bff.service.svc.seo

import com.ctrip.car.businessim.bff.service.core.mapper.seo.QuerySeoPageConfigMapper
import com.ctrip.car.businessim.bff.service.dto.seo.QuerySeoPageConfigBffRequestType
import com.ctrip.car.businessim.bff.service.dto.seo.QuerySeoPageConfigBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.seo.QuerySeoPageConfigRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.seo.QuerySeoPageConfigResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*

class QuerySeoPageConfigServiceTest extends Specification {

    def mapper = Mock(QuerySeoPageConfigMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def querySeoPageConfigService = new QuerySeoPageConfigService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        mapper.from(_ as QuerySeoPageConfigBffRequestType) >> (new QuerySeoPageConfigRequestType())

        expect:
        querySeoPageConfigService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                          || expectedResult
        new QuerySeoPageConfigBffRequestType() || new QuerySeoPageConfigRequestType()
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        proxy.querySeoPageConfig(_ as QuerySeoPageConfigRequestType) >> (new QuerySeoPageConfigResponseType())

        expect:
        querySeoPageConfigService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                       || expectedResult
        new QuerySeoPageConfigRequestType() || new QuerySeoPageConfigResponseType()
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
        mapper.to(_ as QuerySeoPageConfigResponseType) >> (new QuerySeoPageConfigBffResponseType())

        expect:
        querySeoPageConfigService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                                                                                                                                     | var3                                                                                                                                                                                                                                             | var2                                                                                                                                                                                                                                          || expectedResult
        new QuerySeoPageConfigResponseType() | new QuerySeoPageConfigBffRequestType() | new QuerySeoPageConfigRequestType() || new QuerySeoPageConfigBffResponseType()
    }

    def "test convertRequest with null input"() {
        given:
        mapper.from(null) >> null

        when:
        def result = querySeoPageConfigService.convertRequest(null)

        then:
        result == null
        1 * mapper.from(null)
    }

    def "test executeSOA with null input"() {
        given:
        proxy.querySeoPageConfig(null) >> null

        when:
        def result = querySeoPageConfigService.executeSOA(null)

        then:
        result == null
        1 * proxy.querySeoPageConfig(null)
    }

    def "test convertResponse with null response"() {
        given:
        def var2 = new QuerySeoPageConfigRequestType()
        def var3 = new QuerySeoPageConfigBffRequestType()
        mapper.to(null) >> null

        when:
        def result = querySeoPageConfigService.convertResponse(null, var2, var3)

        then:
        result == null
        1 * mapper.to(null)
    }

    def "test convertRequest calls mapper correctly"() {
        given:
        def bffRequest = new QuerySeoPageConfigBffRequestType()
        def expectedSoaRequest = new QuerySeoPageConfigRequestType()
        mapper.from(bffRequest) >> expectedSoaRequest

        when:
        def result = querySeoPageConfigService.convertRequest(bffRequest)

        then:
        result == expectedSoaRequest
        1 * mapper.from(bffRequest) >> expectedSoaRequest
    }

    def "test executeSOA calls proxy correctly"() {
        given:
        def soaRequest = new QuerySeoPageConfigRequestType()
        def expectedSoaResponse = new QuerySeoPageConfigResponseType()
        proxy.querySeoPageConfig(soaRequest) >> expectedSoaResponse

        when:
        def result = querySeoPageConfigService.executeSOA(soaRequest)

        then:
        result == expectedSoaResponse
        1 * proxy.querySeoPageConfig(soaRequest) >> expectedSoaResponse
    }

    def "test convertResponse calls mapper correctly"() {
        given:
        def soaResponse = new QuerySeoPageConfigResponseType()
        def var2 = new QuerySeoPageConfigRequestType()
        def var3 = new QuerySeoPageConfigBffRequestType()
        def expectedBffResponse = new QuerySeoPageConfigBffResponseType()
        mapper.to(soaResponse) >> expectedBffResponse

        when:
        def result = querySeoPageConfigService.convertResponse(soaResponse, var2, var3)

        then:
        result == expectedBffResponse
        1 * mapper.to(soaResponse) >> expectedBffResponse
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme
