package com.ctrip.car.businessim.bff.service.svc.seo

import com.ctrip.car.businessim.bff.service.core.mapper.seo.UpdateSeoPageConfigUpdateMapper
import com.ctrip.car.businessim.bff.service.dto.seo.UpdateSeoPageConfigStatusBffRequestType
import com.ctrip.car.businessim.bff.service.dto.seo.UpdateSeoPageConfigStatusBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.seo.UpdateSeoPageConfigStatusRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.seo.UpdateSeoPageConfigStatusResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*

class UpdateSeoPageConfigStatusServiceTest extends Specification {

    def mapper = Mock(UpdateSeoPageConfigUpdateMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def updateSeoPageConfigStatusService = new UpdateSeoPageConfigStatusService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        mapper.from(_ as UpdateSeoPageConfigStatusBffRequestType) >> (new UpdateSeoPageConfigStatusRequestType())

        expect:
        updateSeoPageConfigStatusService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                          || expectedResult
        new UpdateSeoPageConfigStatusBffRequestType() || new UpdateSeoPageConfigStatusRequestType()
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        proxy.updateSeoPageConfigStatus(_ as UpdateSeoPageConfigStatusRequestType) >> (new UpdateSeoPageConfigStatusResponseType())

        expect:
        updateSeoPageConfigStatusService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                       || expectedResult
        new UpdateSeoPageConfigStatusRequestType() || new UpdateSeoPageConfigStatusResponseType()
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
        mapper.to(_ as UpdateSeoPageConfigStatusResponseType) >> (new UpdateSeoPageConfigStatusBffResponseType())

        expect:
        updateSeoPageConfigStatusService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                                                                                                                                     | var3                                                                                                                                                                                                                                             | var2                                                                                                                                                                                                                                          || expectedResult
        new UpdateSeoPageConfigStatusResponseType() | new UpdateSeoPageConfigStatusBffRequestType() | new UpdateSeoPageConfigStatusRequestType() || new UpdateSeoPageConfigStatusBffResponseType()
    }

    def "test convertRequest with null input"() {
        given:
        mapper.from(null) >> null

        when:
        def result = updateSeoPageConfigStatusService.convertRequest(null)

        then:
        result == null
        1 * mapper.from(null)
    }

    def "test executeSOA with null input"() {
        given:
        proxy.updateSeoPageConfigStatus(null) >> null

        when:
        def result = updateSeoPageConfigStatusService.executeSOA(null)

        then:
        result == null
        1 * proxy.updateSeoPageConfigStatus(null)
    }

    def "test convertResponse with null response"() {
        given:
        def var2 = new UpdateSeoPageConfigStatusRequestType()
        def var3 = new UpdateSeoPageConfigStatusBffRequestType()
        mapper.to(null) >> null

        when:
        def result = updateSeoPageConfigStatusService.convertResponse(null, var2, var3)

        then:
        result == null
        1 * mapper.to(null)
    }

    def "test convertRequest calls mapper correctly"() {
        given:
        def bffRequest = new UpdateSeoPageConfigStatusBffRequestType()
        def expectedSoaRequest = new UpdateSeoPageConfigStatusRequestType()
        mapper.from(bffRequest) >> expectedSoaRequest

        when:
        def result = updateSeoPageConfigStatusService.convertRequest(bffRequest)

        then:
        result == expectedSoaRequest
        1 * mapper.from(bffRequest) >> expectedSoaRequest
    }

    def "test executeSOA calls proxy correctly"() {
        given:
        def soaRequest = new UpdateSeoPageConfigStatusRequestType()
        def expectedSoaResponse = new UpdateSeoPageConfigStatusResponseType()
        proxy.updateSeoPageConfigStatus(soaRequest) >> expectedSoaResponse

        when:
        def result = updateSeoPageConfigStatusService.executeSOA(soaRequest)

        then:
        result == expectedSoaResponse
        1 * proxy.updateSeoPageConfigStatus(soaRequest) >> expectedSoaResponse
    }

    def "test convertResponse calls mapper correctly"() {
        given:
        def soaResponse = new UpdateSeoPageConfigStatusResponseType()
        def var2 = new UpdateSeoPageConfigStatusRequestType()
        def var3 = new UpdateSeoPageConfigStatusBffRequestType()
        def expectedBffResponse = new UpdateSeoPageConfigStatusBffResponseType()
        mapper.to(soaResponse) >> expectedBffResponse

        when:
        def result = updateSeoPageConfigStatusService.convertResponse(soaResponse, var2, var3)

        then:
        result == expectedBffResponse
        1 * mapper.to(soaResponse) >> expectedBffResponse
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme
