package com.ctrip.car.businessim.bff.service.svc.seo

import com.ctrip.car.businessim.bff.service.core.mapper.seo.SaveSeoPageConfigMapper
import com.ctrip.car.businessim.bff.service.dto.seo.SaveSeoPageConfigBffRequestType
import com.ctrip.car.businessim.bff.service.dto.seo.SaveSeoPageConfigBffResponseType
import com.ctrip.car.businessim.bff.service.proxy.soa.CustomerBusinessIMServiceProxy
import com.ctrip.car.customer.businessim.contract.servicetype.seo.SaveSeoPageConfigRequestType
import com.ctrip.car.customer.businessim.contract.servicetype.seo.SaveSeoPageConfigResponseType
import com.ctrip.car.top.BaseRequest
import com.ctrip.car.top.BaseResponse
import com.ctrip.car.top.KeyValueDTO
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import spock.lang.*

class SaveSeoPageConfigServiceTest extends Specification {

    def mapper = Mock(SaveSeoPageConfigMapper.class)
    def proxy = Mock(CustomerBusinessIMServiceProxy.class)
    def saveSeoPageConfigService = new SaveSeoPageConfigService(
            proxy: proxy,
            mapper: mapper)

    @Unroll
    def "convert Request where request=#request then expect: #expectedResult"() {
        given:
        mapper.from(_ as SaveSeoPageConfigBffRequestType) >> (new SaveSeoPageConfigRequestType())

        expect:
        saveSeoPageConfigService.convertRequest(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                          || expectedResult
        new SaveSeoPageConfigBffRequestType() || new SaveSeoPageConfigRequestType()
    }

    @Unroll
    def "execute SOA where request=#request then expect: #expectedResult"() {
        given:
        proxy.saveSeoPageConfig(_ as SaveSeoPageConfigRequestType) >> (new SaveSeoPageConfigResponseType())

        expect:
        saveSeoPageConfigService.executeSOA(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                       || expectedResult
        new SaveSeoPageConfigRequestType() || new SaveSeoPageConfigResponseType()
    }

    @Unroll
    def "convert Response where response=#response and var3=#var3 and var2=#var2 then expect: #expectedResult"() {
        given:
        mapper.to(_ as SaveSeoPageConfigResponseType) >> (new SaveSeoPageConfigBffResponseType())

        expect:
        saveSeoPageConfigService.convertResponse(response, var2, var3) == expectedResult

        where:
        response                                                                                                                                                                                                                     | var3                                                                                                                                                                                                                                             | var2                                                                                                                                                                                                                                          || expectedResult
        new SaveSeoPageConfigResponseType() | new SaveSeoPageConfigBffRequestType() | new SaveSeoPageConfigRequestType() || new SaveSeoPageConfigBffResponseType()
    }

    def "test convertRequest with null input"() {
        given:
        mapper.from(null) >> null

        when:
        def result = saveSeoPageConfigService.convertRequest(null)

        then:
        result == null
        1 * mapper.from(null)
    }

    def "test executeSOA with null input"() {
        given:
        proxy.saveSeoPageConfig(null) >> null

        when:
        def result = saveSeoPageConfigService.executeSOA(null)

        then:
        result == null
        1 * proxy.saveSeoPageConfig(null)
    }

    def "test convertResponse with null response"() {
        given:
        def var2 = new SaveSeoPageConfigRequestType()
        def var3 = new SaveSeoPageConfigBffRequestType()
        mapper.to(null) >> null

        when:
        def result = saveSeoPageConfigService.convertResponse(null, var2, var3)

        then:
        result == null
        1 * mapper.to(null)
    }

    def "test convertRequest calls mapper correctly"() {
        given:
        def bffRequest = new SaveSeoPageConfigBffRequestType()
        def expectedSoaRequest = new SaveSeoPageConfigRequestType()
        mapper.from(bffRequest) >> expectedSoaRequest

        when:
        def result = saveSeoPageConfigService.convertRequest(bffRequest)

        then:
        result == expectedSoaRequest
        1 * mapper.from(bffRequest) >> expectedSoaRequest
    }

    def "test executeSOA calls proxy correctly"() {
        given:
        def soaRequest = new SaveSeoPageConfigRequestType()
        def expectedSoaResponse = new SaveSeoPageConfigResponseType()
        proxy.saveSeoPageConfig(soaRequest) >> expectedSoaResponse

        when:
        def result = saveSeoPageConfigService.executeSOA(soaRequest)

        then:
        result == expectedSoaResponse
        1 * proxy.saveSeoPageConfig(soaRequest) >> expectedSoaResponse
    }

    def "test convertResponse calls mapper correctly"() {
        given:
        def soaResponse = new SaveSeoPageConfigResponseType()
        def var2 = new SaveSeoPageConfigRequestType()
        def var3 = new SaveSeoPageConfigBffRequestType()
        def expectedBffResponse = new SaveSeoPageConfigBffResponseType()
        mapper.to(soaResponse) >> expectedBffResponse

        when:
        def result = saveSeoPageConfigService.convertResponse(soaResponse, var2, var3)

        then:
        result == expectedBffResponse
        1 * mapper.to(soaResponse) >> expectedBffResponse
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme
